import { Service } from '@n8n/di';
import { Repository } from '@n8n/typeorm';

import { TiDbDataSourceService } from '../connection/tidb-datasource.service';
import { GalaxyPermission } from '../entities';

@Service()
export class GalaxyPermissionRepository extends Repository<GalaxyPermission> {
	constructor(tiDbDataSourceService: TiDbDataSourceService) {
		super(GalaxyPermission, tiDbDataSourceService.dataSource.manager);
	}

	async addGalaxyPermitRecord(gPermit: GalaxyPermission) {
		return await this.save(gPermit);
	}

	async deleteGalaxyPermitRecord(workflowID: string) {
		return await this.delete({ res_id: workflowID });
	}
}
