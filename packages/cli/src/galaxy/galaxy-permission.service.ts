import { Logger } from '@n8n/backend-common';
import { GalaxyPermission, GalaxyPermissionRepository } from '@n8n/db';
import { Service } from '@n8n/di';

@Service()
export class GalaxyService {
	constructor(
		private readonly logger: Logger,
		private readonly galaxyPermissionRepository: GalaxyPermissionRepository,
	) {}

	async addGalaxyPermitRecord(gPermit: Omit<GalaxyPermission, 'id'>) {
		this.logger.info('Add Galaxy Permit Record', { gPermit });
		return await this.galaxyPermissionRepository.addGalaxyPermitRecord(gPermit);
	}

	async deleteGalaxyPermitRecord(workflowID: string) {
		this.logger.info('Delete Galaxy Permit Record', { workflowID });
		return await this.galaxyPermissionRepository.deleteGalaxyPermitRecord(workflowID);
	}
}
